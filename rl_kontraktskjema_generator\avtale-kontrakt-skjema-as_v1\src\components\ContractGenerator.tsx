import React, { useState, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { Download } from 'lucide-react';

interface ContractData {
  employeeName: string;
  employeeAddress: string;
  employeeBirthDate: string;
  startDate: string;
  employmentType: 'permanent' | 'temporary';
  temporaryUntil: string;
  temporaryReason: string;
  hasProbation: boolean;
  probationMonths: string;
  jobTitle: string;
  accountNumber: string;
}

// Unified Contract Component for both preview and PDF
const ContractDocument: React.FC<{
  data: ContractData;
  isPdfMode?: boolean;
  formatDate: (dateString: string) => string;
}> = ({ data, isPdfMode = false, formatDate }) => {
  const containerClass = isPdfMode
    ? "contract-document-pdf"
    : "contract-document-preview";

  return (
    <div className={containerClass}>
      <h1 className="contract-title">ARBEIDSAVTALE</h1>

      <div className="parties-section">
        <div className="party-block">
          <strong>Arbeidsgiver</strong><br/>
          Ringerike Landskap AS<br/>
          Org.nr 924 826 541<br/>
          Birchs vei 7, 3530 Røyse
        </div>

        <div className="party-block">
          <strong>Arbeidstaker</strong><br/>
          Navn: {data.employeeName || '_'.repeat(28)}<br/>
          Adresse: {data.employeeAddress || '_'.repeat(25)}<br/>
          Fødselsdato: {data.employeeBirthDate || '_'.repeat(21)}
        </div>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>1 Ansettelsesforhold</h2>
        <ul className="contract-list">
          <li><strong>Startdato:</strong> {formatDate(data.startDate) || '_'.repeat(19)}</li>
          <li><strong>Ansettelsestype:</strong> {data.employmentType === 'permanent' ? '☑ Fast ☐ Midlertidig' : '☐ Fast ☑ Midlertidig'}{data.employmentType === 'temporary' ? ` t.o.m. ${formatDate(data.temporaryUntil) || '_'.repeat(12)}` : ''}</li>
          {data.employmentType === 'temporary' && (
            <li><em>Grunnlag (hvis midlertidig): {data.temporaryReason || '_'.repeat(33)}</em></li>
          )}
          <li><strong>Prøvetid:</strong> {data.hasProbation ? `☑ ${data.probationMonths || '___'} mnd` : '☑ Ingen'} (maks 6)</li>
          {data.hasProbation && (
            <li><em>Gjensidig oppsigelsesfrist i prøvetid: 14 dager</em></li>
          )}
        </ul>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>2 Arbeidssted</h2>
        <p>Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt. Hvis variabelt arbeidssted, gjelder dette som hovedregel.</p>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>3 Stilling & oppgaver</h2>
        <p><strong>Stilling:</strong> {data.jobTitle || '_'.repeat(31)}<br/>
        Arbeid innen anleggsgartner‑ og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten.</p>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>4 Arbeidstid & pauser</h2>
        <ul className="contract-list">
          <li>Ordinær tid: <strong>37,5 t per uke</strong>, normalt kl. 07:00 – 15:00.</li>
          <li>Minst én 30 min pause ved arbeidsdag over 5,5 t.</li>
          <li>Overtid honoreres med <strong>≥ 40 %</strong> tillegg etter AML § 10‑6.</li>
        </ul>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>5 Lønn & godtgjørelse</h2>
        <table className="compensation-table">
          <tbody>
            <tr>
              <td><strong>Element</strong></td>
              <td><strong>Sats / tidspunkt</strong></td>
            </tr>
            <tr>
              <td>Timesats</td>
              <td><strong>kr 300,-</strong></td>
            </tr>
            <tr>
              <td>Utbetaling</td>
              <td>5. hver måned til konto nr.: {data.accountNumber || '_'.repeat(10)}</td>
            </tr>
            <tr>
              <td>Overtidstillegg</td>
              <td>≥ 40 % av timelønn</td>
            </tr>
            <tr>
              <td>Kjøring egen bil</td>
              <td>Statens trekkfrie sats – pt. <strong>3,50 kr/km</strong></td>
            </tr>
            <tr>
              <td>Pensjon</td>
              <td>OTP ‑ minimum 2 % av lønn</td>
            </tr>
          </tbody>
        </table>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>6 Ferie & feriepenger</h2>
        <ul className="contract-list">
          <li><strong>5 uker ferie</strong> pr. år (Ferieloven).</li>
          <li>Feriepenger <strong>12 %</strong>; utbetales før hovedferie / ved fratreden når aktuelt.</li>
        </ul>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>7 Oppsigelse</h2>
        <table className="termination-table">
          <tbody>
            <tr>
              <td><strong>Situasjon</strong></td>
              <td><strong>Frist</strong></td>
            </tr>
            <tr>
              <td>I prøvetid</td>
              <td>14 dager</td>
            </tr>
            <tr>
              <td>Etter prøvetid</td>
              <td>1 måned gjensidig</td>
            </tr>
          </tbody>
        </table>
        <p className="legal-note">Oppsigelse skal skje skriftlig iht. AML kap. 15.</p>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>8 Diverse vilkår</h2>
        <ul className="contract-list">
          <li>Arbeidstaker følger instruks, HMS‑rutiner og bruk av verneutstyr.</li>
          <li>Arbeidsgiver stiller nødvendig arbeidstøy og verktøy.</li>
          <li>Ingen tariffavtale er gjeldende pr. dags dato.</li>
          <li>Endringer i arbeidsforholdet dokumenteres skriftlig som vedlegg til denne avtalen.</li>
        </ul>
      </div>

      <hr className="section-divider"/>

      <div className="signature-section">
        <h2>Signaturer</h2>
        <table className="signature-table">
          <tbody>
            <tr>
              <td><strong>Sted / dato:</strong> Røyse, {formatDate(data.startDate) || '_'.repeat(14)}</td>
              <td></td>
            </tr>
            <tr>
              <td><strong>Arbeidsgiver:</strong> _________________________________</td>
              <td><strong>Arbeidstaker:</strong> _________________________________</td>
            </tr>
          </tbody>
        </table>
      </div>

      <hr className="section-divider"/>

      <div className="footer-note">
        <em>Avtalen er inngått i to eksemplarer; hver part beholder ett.</em>
      </div>
    </div>
  );
};

const ContractGenerator = () => {
  const [contractData, setContractData] = useState<ContractData>({
    employeeName: '',
    employeeAddress: '',
    employeeBirthDate: '',
    startDate: '',
    employmentType: 'permanent',
    temporaryUntil: '',
    temporaryReason: '',
    hasProbation: false,
    probationMonths: '',
    jobTitle: '',
    accountNumber: ''
  });

  const [showContract, setShowContract] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const contractRef = useRef<HTMLDivElement>(null);

  const handleInputChange = (field: keyof ContractData, value: string | boolean) => {
    setContractData(prev => ({ ...prev, [field]: value }));
  };

  const generateContract = () => {
    setShowContract(true);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '__.__.__';
    const date = new Date(dateString);
    return date.toLocaleDateString('no-NO');
  };

  const downloadPDF = async () => {
    if (!contractRef.current) return;

    setIsGeneratingPDF(true);

    try {
      // Create a temporary container for full-height PDF generation
      // A4 dimensions: 210mm x 297mm = 794px x 1123px at 96 DPI
      const A4_WIDTH_PX = 794;
      const A4_HEIGHT_PX = 1123;
      const PADDING_PX = 76; // 20mm = ~76px at 96 DPI

      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '0';
      tempContainer.style.width = `${A4_WIDTH_PX}px`;
      tempContainer.style.backgroundColor = 'white';
      tempContainer.style.padding = `${PADDING_PX}px`;
      tempContainer.style.fontFamily = "'Times New Roman', serif";
      tempContainer.style.fontSize = '11pt';
      tempContainer.style.lineHeight = '1.4';
      tempContainer.style.color = '#000';
      tempContainer.style.boxSizing = 'border-box';

      // Clone the contract content without height restrictions
      const contractClone = contractRef.current.cloneNode(true) as HTMLElement;

      // Remove any height restrictions and apply PDF styling
      contractClone.style.maxHeight = 'none';
      contractClone.style.overflow = 'visible';
      contractClone.style.height = 'auto';
      contractClone.style.width = '100%';
      contractClone.style.padding = '0';
      contractClone.style.margin = '0';
      contractClone.style.border = 'none';
      contractClone.style.borderRadius = '0';
      contractClone.className = 'contract-document-pdf';

      tempContainer.appendChild(contractClone);
      document.body.appendChild(tempContainer);

      // Wait for fonts and styles to load
      await new Promise(resolve => setTimeout(resolve, 100));

      // Generate canvas with precise scaling
      const canvas = await html2canvas(tempContainer, {
        scale: 1, // Use scale 1 for precise pixel mapping
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: A4_WIDTH_PX,
        height: tempContainer.scrollHeight,
        scrollX: 0,
        scrollY: 0,
        logging: false,
      });

      // Remove temporary container
      document.body.removeChild(tempContainer);

      // Create PDF with exact A4 dimensions
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgData = canvas.toDataURL('image/png', 1.0);

      // A4 dimensions in mm
      const pdfWidth = 210; // A4 width in mm
      const pdfHeight = 297; // A4 height in mm

      // Calculate the actual content height in mm based on canvas
      const contentHeightMm = (canvas.height * pdfWidth) / canvas.width;

      // Add image to PDF with exact 1:1 proportional mapping
      if (contentHeightMm <= pdfHeight) {
        // Content fits on one page - use full width
        pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, contentHeightMm);
      } else {
        // Content is taller than one page - scale to fit height
        const scaleFactor = pdfHeight / contentHeightMm;
        const scaledWidth = pdfWidth * scaleFactor;
        const xOffset = (pdfWidth - scaledWidth) / 2;
        pdf.addImage(imgData, 'PNG', xOffset, 0, scaledWidth, pdfHeight);
      }

      // Generate filename with employee name and date
      const employeeName = contractData.employeeName || 'Ukjent';
      const today = new Date().toLocaleDateString('no-NO').replace(/\./g, '-');
      const filename = `Arbeidskontrakt_${employeeName.replace(/\s+/g, '_')}_${today}.pdf`;

      // Download the PDF
      pdf.save(filename);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Det oppstod en feil ved generering av PDF. Prøv igjen.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-4">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center text-green-800">
              Arbeidskontrakt Generator
            </CardTitle>
            <p className="text-center text-gray-600">Ringerike Landskap AS</p>
          </CardHeader>
          <CardContent>
            {!showContract ? (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="employeeName">Navn på arbeidstaker *</Label>
                    <Input
                      id="employeeName"
                      value={contractData.employeeName}
                      onChange={(e) => handleInputChange('employeeName', e.target.value)}
                      placeholder="Fullt navn"
                    />
                  </div>
                  <div>
                    <Label htmlFor="employeeBirthDate">Fødselsdato *</Label>
                    <Input
                      id="employeeBirthDate"
                      value={contractData.employeeBirthDate}
                      onChange={(e) => handleInputChange('employeeBirthDate', e.target.value)}
                      placeholder="dd.mm.åååå"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="employeeAddress">Adresse *</Label>
                  <Input
                    id="employeeAddress"
                    value={contractData.employeeAddress}
                    onChange={(e) => handleInputChange('employeeAddress', e.target.value)}
                    placeholder="Gate/vei, postnummer og sted"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate">Startdato *</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={contractData.startDate}
                      onChange={(e) => handleInputChange('startDate', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="jobTitle">Stillingstittel *</Label>
                    <Input
                      id="jobTitle"
                      value={contractData.jobTitle}
                      onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                      placeholder="F.eks. Anleggsgartner, Grunnarbeider"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="accountNumber">Kontonummer</Label>
                  <Input
                    id="accountNumber"
                    value={contractData.accountNumber}
                    onChange={(e) => handleInputChange('accountNumber', e.target.value)}
                    placeholder="XXXX.XX.XXXXX"
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="temporary"
                      checked={contractData.employmentType === 'temporary'}
                      onCheckedChange={(checked) => 
                        handleInputChange('employmentType', checked ? 'temporary' : 'permanent')
                      }
                    />
                    <Label htmlFor="temporary">Midlertidig ansettelse</Label>
                  </div>

                  {contractData.employmentType === 'temporary' && (
                    <div className="ml-6 space-y-3">
                      <div>
                        <Label htmlFor="temporaryUntil">Midlertidig til dato</Label>
                        <Input
                          id="temporaryUntil"
                          type="date"
                          value={contractData.temporaryUntil}
                          onChange={(e) => handleInputChange('temporaryUntil', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="temporaryReason">Grunnlag for midlertidig ansettelse</Label>
                        <Textarea
                          id="temporaryReason"
                          value={contractData.temporaryReason}
                          onChange={(e) => handleInputChange('temporaryReason', e.target.value)}
                          placeholder="F.eks. Vikariat, sesongarbeid, prosjekt"
                          rows={2}
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="probation"
                      checked={contractData.hasProbation}
                      onCheckedChange={(checked) => handleInputChange('hasProbation', checked as boolean)}
                    />
                    <Label htmlFor="probation">Prøvetid</Label>
                  </div>

                  {contractData.hasProbation && (
                    <div className="ml-6">
                      <Label htmlFor="probationMonths">Antall måneder (maks 6)</Label>
                      <Input
                        id="probationMonths"
                        type="number"
                        min="1"
                        max="6"
                        value={contractData.probationMonths}
                        onChange={(e) => handleInputChange('probationMonths', e.target.value)}
                        placeholder="1-6"
                      />
                    </div>
                  )}
                </div>

                <Button onClick={generateContract} className="w-full bg-green-600 hover:bg-green-700">
                  Generer arbeidskontrakt
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Generert arbeidskontrakt</h3>
                  <div className="flex gap-2">
                    <Button
                      onClick={downloadPDF}
                      disabled={isGeneratingPDF}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isGeneratingPDF ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Genererer PDF...
                        </>
                      ) : (
                        <>
                          <Download className="h-4 w-4 mr-2" />
                          Last ned PDF
                        </>
                      )}
                    </Button>
                    <Button onClick={() => setShowContract(false)} variant="outline">
                      Tilbake til skjema
                    </Button>
                  </div>
                </div>
                
                <div
                  ref={contractRef}
                  className="contract-display border rounded-lg bg-white p-6"
                  style={{
                    maxHeight: '70vh',
                    overflow: 'auto'
                  }}
                >
                  <ContractDocument
                    data={contractData}
                    formatDate={formatDate}
                    isPdfMode={false}
                  />
                </div>
                
                <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded">
                  <strong>Juridisk korrekt:</strong> Denne kontrakten oppfyller alle krav i arbeidsmiljøloven § 14-6 
                  og er formatert for profesjonell utskrift på én A4-side. Kontrakten inkluderer alle nødvendige 
                  elementer som minimumslønnsvilkår, oppsigelsesfrister, og pensjonsordninger.
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ContractGenerator;
