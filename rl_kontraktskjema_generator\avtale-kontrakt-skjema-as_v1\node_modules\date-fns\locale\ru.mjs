import { formatDistance } from "./ru/_lib/formatDistance.mjs";
import { formatLong } from "./ru/_lib/formatLong.mjs";
import { formatRelative } from "./ru/_lib/formatRelative.mjs";
import { localize } from "./ru/_lib/localize.mjs";
import { match } from "./ru/_lib/match.mjs";

/**
 * @category Locales
 * @summary Russian locale.
 * @language Russian
 * @iso-639-2 rus
 * <AUTHOR> [@kossnocorp](https://github.com/kossnocorp)
 * <AUTHOR> [@leshakoss](https://github.com/leshakoss)
 */
export const ru = {
  code: "ru",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ru;
