{"version": 3, "file": "dot-notation.js", "sourceRoot": "", "sources": ["../../src/rules/dot-notation.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAOjC,kCAAsE;AACtE,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,cAAc,CAAC,CAAC;AAKnD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,wCAAwC;YACrD,eAAe,EAAE,IAAI;YACrB,WAAW,EAAE,WAAW;YACxB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;QAC9B,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAChC,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,iCAAiC,EAAE;wBACjC,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,KAAK;wBACd,WAAW,EACT,wFAAwF;qBAC3F;oBACD,aAAa,EAAE;wBACb,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,IAAI;wBACb,WAAW,EAAE,+CAA+C;qBAC7D;oBACD,YAAY,EAAE;wBACZ,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,EAAE;wBACX,WAAW,EAAE,uCAAuC;qBACrD;oBACD,+BAA+B,EAAE;wBAC/B,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,KAAK;wBACd,WAAW,EACT,mFAAmF;qBACtF;oBACD,iCAAiC,EAAE;wBACjC,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,KAAK;wBACd,WAAW,EACT,qFAAqF;qBACxF;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,iCAAiC,EAAE,KAAK;YACxC,aAAa,EAAE,IAAI;YACnB,YAAY,EAAE,EAAE;YAChB,+BAA+B,EAAE,KAAK;YACtC,iCAAiC,EAAE,KAAK;SACzC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAE5C,MAAM,+BAA+B,GACnC,OAAO,CAAC,+BAA+B,CAAC;QAC1C,MAAM,iCAAiC,GACrC,OAAO,CAAC,iCAAiC,CAAC;QAC5C,MAAM,iCAAiC,GACrC,CAAC,OAAO,CAAC,iCAAiC,IAAI,KAAK,CAAC;YACpD,OAAO,CAAC,uBAAuB,CAC7B,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,EACrC,oCAAoC,CACrC,CAAC;QAEJ,OAAO;YACL,gBAAgB,CAAC,IAA+B;gBAC9C,IACE,CAAC,+BAA+B;oBAC9B,iCAAiC;oBACjC,iCAAiC,CAAC;oBACpC,IAAI,CAAC,QAAQ,EACb,CAAC;oBACD,sDAAsD;oBACtD,MAAM,cAAc,GAClB,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC;wBAC3C,QAAQ;6BACL,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC;6BAC9B,kBAAkB,EAAE;6BACpB,aAAa,EAAE;6BACf,IAAI,CACH,cAAc,CAAC,EAAE,CACf,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;4BAC7C,cAAc,CAAC,WAAW,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CACrD,CAAC;oBACN,MAAM,YAAY,GAAG,IAAA,mBAAY,EAC/B,cAAc,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,CACvC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBACZ,IACE,CAAC,+BAA+B;wBAC9B,YAAY,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC;wBAChD,CAAC,iCAAiC;4BAChC,YAAY,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAClD,CAAC;wBACD,OAAO;oBACT,CAAC;oBACD,IACE,cAAc,KAAK,SAAS;wBAC5B,iCAAiC,EACjC,CAAC;wBACD,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC3D,MAAM,SAAS,GAAG,UAAU;6BACzB,kBAAkB,EAAE;6BACpB,kBAAkB,EAAE,CAAC;wBACxB,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;4BAC5B,OAAO;wBACT,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}